Index: src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts
===================================================================
diff --git a/src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts b/src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts
deleted file mode 100644
--- a/src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts	(revision 57b2193ffd904d450a32bf0281dea7a57eff23df)
+++ /dev/null	(revision 57b2193ffd904d450a32bf0281dea7a57eff23df)
@@ -1,129 +0,0 @@
-import styled from '@emotion/styled';
-import {css} from '@emotion/css';
-import {colors} from '@/constants/colors';
-
-export const containerCss = css`
-    padding: 16px 20px 12px;
-    position: relative;
-    transition: all 0.3s ease;
-    &:hover {
-        position: relative;
-        z-index: 1;
-        background: ${colors.white};
-        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
-        border-radius: 6px;
-        padding-bottom: 48px;
-        margin-bottom: -48px;
-        width: auto;
-        .hover-actions {
-            opacity: 1;
-            min-height: 32px;
-            padding: 0 20px 16px;
-        }
-    }
-`;
-
-export const hoverActionsStyle = css`
-    position: absolute;
-    bottom: 0;
-    left: 0;
-    right: 0;
-    max-height: 0;
-    opacity: 0;
-    transition: all 0.3s ease;
-`;
-
-export const DescriptionContainer = styled.div`
-    margin: 15px 0 13px;
-    padding: 10px 12px 9px;
-    background-color: ${colors['gray-3']};
-    font-size: 14px;
-    line-height: 1.4;
-    position: relative;
-    height: 57px;
-    overflow: hidden;
-`;
-
-export const DescriptionText = styled.div`
-    display: -webkit-box;
-    -webkit-line-clamp: 2;
-    -webkit-box-orient: vertical;
-    text-overflow: ellipsis;
-    word-break: break-word;
-    overflow: hidden;
-`;
-
-export const EllipsisOverlay = styled.div`
-    position: absolute;
-    bottom: 9px;
-    right: 12px;
-    padding-left: 10px;
-    background: linear-gradient(to right, transparent, ${colors['gray-3']} 50%);
-    pointer-events: none;
-`;
-
-export const cardContentStyle = {
-    overflow: 'hidden',
-    flex: 1,
-};
-
-export const protocolTextStyle = {
-    color: colors['gray-7'],
-    fontSize: 12,
-    lineHeight: '20px',
-};
-
-export const departmentTextStyle = {
-    color: colors['gray-7'],
-    fontSize: 12,
-    marginBottom: 12,
-};
-
-export const dividerStyle = {
-    margin: '16px 0 8px',
-};
-
-export const statsContainerStyle = css`
-    cursor: pointer;
-    color: ${colors['gray-7']};
-    font-size: 12px;
-    line-height: 18px;
-    transition: color 0.2s ease;
-
-    &:hover {
-        color: ${colors.primary};
-    }
-`;
-
-export const iconStyle = {
-    width: 14,
-    height: 14,
-};
-
-export const formatCount = (count: number): string => {
-    if (count >= 10000) {
-        return `${Math.floor(count / 10000)}w+`;
-    }
-    if (count >= 1000) {
-        return `${Math.floor(count / 1000)}k+`;
-    }
-    return count.toString();
-};
-
-export const actionButtonHoverStyle = css`
-    flex: 1;
-    background-color: #F2F2F2;
-    border-radius: 4px;
-    border: none;
-    padding: 0;
-    display: flex;
-    align-items: center;
-    justify-content: center;
-    transition: all 0.2s ease;
-
-    &:hover {
-        background-color: #E6E6E6 !important;
-        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
-        transform: translateY(-1px);
-    }
-`;
Index: .gitignore
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># See https://help.github.com/articles/ignoring-files/ for more about ignoring files.\n\n# dependencies\n/node_modules\n/.pnp\n.pnp.js\n\n# testing\n/coverage\n\n# production\n/build\n/output\n/.comate-f2c\n\n# misc\n.DS_Store\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.gitignore b/.gitignore
--- a/.gitignore	(revision 57b2193ffd904d450a32bf0281dea7a57eff23df)
+++ b/.gitignore	(date 1755241290377)
@@ -23,3 +23,27 @@
 npm-debug.log*
 yarn-debug.log*
 yarn-error.log*
+.idea/vcs.xml
+.idea/workspace.xml
+.idea/codeStyles/codeStyleConfig.xml
+.idea/codeStyles/Project.xml
+.idea/inspectionProfiles/Project_Default.xml
+.idea/shelf/Changes.xml
+.idea/shelf/Changes1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21__Changes_1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_10_42__Changes_.xml
+.idea/shelf/Changes/shelved.patch
+.idea/shelf/Changes1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21_\[Changes]1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16_\[Changes]/cardBg.png
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_10_42_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_13_49__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_13_49_\[Changes]/shelved.patch
