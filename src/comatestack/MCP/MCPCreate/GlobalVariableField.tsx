/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Button, Text} from '@panda-design/components';
import {Flex, Form, Input, Select, Space, TableColumnsType} from 'antd';
import {useCallback, useEffect, useMemo} from 'react';
import {Path} from '@panda-design/path-form';
import {IconAdd} from '@/icons/lucide';
import {IconSubtract} from '@/icons/mcp';
import {RequiredTitle, StyledTable} from '../MCPEdit/ToolsContent/ParamList';

const StyledButton = styled(Button)`
    color: #317ff5 !important;
    position: relative;
    &:hover{
        color: #317ff5 !important;
    }
`;

const ConflictInput = styled(Input)`
    border-color: #ff4d4f !important;
    &:hover, &:focus {
        border-color: #ff4d4f !important;
        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
    }
`;

const typeSelectOptions = [
    {label: 'String', value: 'string'},
    {label: 'Number', value: 'number'},
    {label: 'Boolean', value: 'boolean'},
    {label: 'Array', value: 'array'},
    {label: 'Object', value: 'object'},
    {label: 'Date', value: 'date'},
];

const requiredOptions = [
    {label: '必需', value: true},
    {label: '可选', value: false},
];

interface Param {
    name: string;
    description?: string;
    dataType: string;
    required: boolean;
    isSystemVar?: boolean;
}

interface Props {
    value?: Param[];
    onChange?: (value: Param[]) => void;
    path: Path;
    rowKey?: string;
    systemVars?: Param[];
}


const GlobalVariableField = ({value, onChange, path, rowKey = 'key', systemVars = []}: Props) => {
    const allVars = useMemo(
        () => {
            const userVars = (value || []).filter(v => !v.isSystemVar);
            const filteredSystemVars = systemVars.filter(v =>
                v.name === 'AccessKey' || v.name === 'SecretKey'
            );
            const combinedVars = [...filteredSystemVars, ...userVars];
            return combinedVars;
        },
        [value, systemVars]
    );

    const filteredSystemVars = useMemo(
        () => systemVars.filter(v => v.name === 'AccessKey' || v.name === 'SecretKey'),
        [systemVars]
    );
    const userVarStartIndex = filteredSystemVars.length;

    const hasConflict = useCallback(
        (varName: string, isSystemVar: boolean) => {
            if (isSystemVar) {
                return false;
            }
            return filteredSystemVars.some(sysVar => sysVar.name === varName);
        },
        [filteredSystemVars]
    );

    useEffect(
        () => {
            const userVars = (value || []).filter(v => !v.isSystemVar);
            const combinedVars = [...filteredSystemVars, ...userVars];

            if (JSON.stringify(combinedVars) !== JSON.stringify(value)) {
                onChange?.(combinedVars);
            }
        },
        [onChange, filteredSystemVars, value]
    );

    const getNewName = useCallback(
        () => {
            const names = allVars.map(item => item.name);
            let index = 1;
            let name = `key${index}`;
            while (names.includes(name)) {
                index += 1;
                name = `key${index}`;
            }
            return name;
        },
        [allVars]
    );

    const onAdd = useCallback(
        () => {
            const name = getNewName();
            // 这里必须要有key,key只能用index，不然和serverConfig里的值对不上
            const newVar = {name, key: allVars.length, dataType: 'string', required: false, isSystemVar: false};
            onChange?.([...allVars, newVar]);
        },
        [onChange, allVars, getNewName]
    );

    const onDelete = useCallback(
        (index: number) => {
            if (index < userVarStartIndex) {
                return;
            }
            const newVars = [...allVars.slice(0, index), ...allVars.slice(index + 1)];
            onChange?.(newVars);
        },
        [onChange, allVars, userVarStartIndex]
    );

    const columns: TableColumnsType<Param> = [
        {
            title: <RequiredTitle>变量名称</RequiredTitle>,
            dataIndex: 'name',
            width: 200,
            render: (_, record, index) => {
                const isSystemVar = record.isSystemVar || index < userVarStartIndex;
                const isConflict = hasConflict(record.name, isSystemVar);
                const canDelete = !isSystemVar;

                return (
                    <Space direction="vertical" style={{width: '100%'}}>
                        <Space>
                            <Button
                                icon={<IconSubtract />}
                                tooltip={canDelete ? '删除' : '系统变量不可删除'}
                                type="text"
                                disabled={!canDelete}
                                onClick={() => onDelete(index)}
                            />
                            <Form.Item
                                style={{marginBottom: 0}}
                                name={[...path, index, 'name']}
                                rules={[{required: true, message: '必填项，不可为空'}]}
                            >
                                {isConflict ? (
                                    <ConflictInput
                                        placeholder="请输入变量名称"
                                        disabled={isSystemVar}
                                        title="变量名与系统变量冲突"
                                    />
                                ) : (
                                    <Input
                                        placeholder="请输入变量名称"
                                        disabled={isSystemVar}
                                    />
                                )}
                            </Form.Item>
                        </Space>
                        {isConflict && (
                            <Text type="error" style={{fontSize: 12, marginLeft: 40}}>
                                变量名与系统变量冲突
                            </Text>
                        )}
                    </Space>
                );
            },
        },
        {
            title: <RequiredTitle>是否必须</RequiredTitle>,
            dataIndex: 'required',
            width: 100,
            render: (_, record, index) => {
                const isSystemVar = record.isSystemVar || index < userVarStartIndex;
                return (
                    <Form.Item
                        style={{marginBottom: 0}}
                        name={[...path, index, 'required']}
                        rules={[{required: true, message: '必填项，不可为空'}]}
                    >
                        <Select
                            options={requiredOptions}
                            allowClear
                            placeholder="请选择"
                            disabled={isSystemVar}
                        />
                    </Form.Item>
                );
            },
        },
        {
            title: <RequiredTitle>类型</RequiredTitle>,
            dataIndex: 'dataType',
            width: 120,
            render: (_, record, index) => {
                const isSystemVar = record.isSystemVar || index < userVarStartIndex;
                return (
                    <Form.Item
                        style={{marginBottom: 0}}
                        name={[...path, index, 'dataType']}
                        rules={[{required: true, message: '必填项，不可为空'}]}
                    >
                        <Select
                            options={typeSelectOptions}
                            allowClear
                            placeholder="请选择"
                            disabled={isSystemVar}
                        />
                    </Form.Item>
                );
            },
        },
        {
            title: '描述',
            dataIndex: 'description',
            render: (_, record, index) => {
                const isSystemVar = record.isSystemVar || index < userVarStartIndex;
                return (
                    <Form.Item
                        style={{marginBottom: 0}}
                        name={[...path, index, 'description']}
                        rules={[{max: 50, message: '最长为50字符'}]}
                    >
                        <Input
                            maxLength={50}
                            placeholder="请输入描述"
                            disabled={isSystemVar}
                        />
                    </Form.Item>
                );
            },
        },

    ];
    return (
        <Space direction="vertical" style={{width: '100%'}}>
            <Flex justify="space-between" align="center">
                <StyledButton type="text" icon={<IconAdd />} onClick={onAdd}>添加变量</StyledButton>
                <span style={{color: '#2D2D2D', fontSize: 12}}>调用该MCP的工具时需传入的全局变量，不会暴露给模型，在脚本中以环境变量形式引用</span>
            </Flex>
            <StyledTable
                rowKey={rowKey}
                dataSource={allVars}
                pagination={{hideOnSinglePage: true}}
                columns={columns}
            />
        </Space>
    );
};

export default GlobalVariableField;

