import {useCallback} from 'react';
import {message} from '@panda-design/components';
import {FormInstance} from 'antd/lib';
import {apiGetSpaceLabels, apiPostMCPServer} from '@/api/mcp';
import {MCPServerBase, MCPServerProtocolType, MCPServerType} from '@/types/mcp/mcp';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import {extractServerParams} from '../MCPEdit/BasicInfoContent/hooks';

export const serverConfigParse = (serverConfig: string, serverProtocolType: MCPServerProtocolType) => {
    if (!serverConfig) {
        return {};
    }
    try {
        const handleServerConfig = JSON.parse(serverConfig || '{}');
        const serverConfigKeys = Object.keys(handleServerConfig?.mcpServers || {});
        const firstPropertyValue = handleServerConfig?.mcpServers?.[serverConfigKeys[0]];
        return {
            mcpServers: {
                ...handleServerConfig.mcpServers,
                [serverConfigKeys[0]]: {
                    ...firstPropertyValue,
                    protocolType: serverProtocolType,
                },
            },
        };
    } catch (e) {
        message.error('请输入正确的JSON格式');
    }
};


export const useHandleCreateMCP = (form: FormInstance) => {
    // useMCPWorkspaceId是从url中取的，从广场创建的时候，url中不可能有这个id，从空间创建的时候会有
    const workspaceId = useMCPWorkspaceId();
    const handleCreate = useCallback(
        (callback: (mcp: MCPServerBase) => void, onError: () => void, mode: MCPServerType) => {
            form.validateFields().then(
                values => {
                    // TODO 这只是一个临时兼容的写法，不应该这么实现，后续考虑优化
                    const id = values.workspaceId ?? workspaceId;
                    return apiGetSpaceLabels({workspaceId: id}).then(labels => ({
                        spaceLabels: labels,
                        values,
                        // @ts-ignore
                    })).catch(() => ({spaceLabels: [], values}));
                }
            ).then(
                ({spaceLabels, values}) => {
                    try {
                        const {serverConf, labels, ...data} = values;
                        const {
                            serverSourceType,
                            apiInfos,
                            serverExtension,
                            overview,
                            serverConfig,
                        } = serverConf || {};
                        const spaceId = data.workspaceId ?? workspaceId;
                        const {serverAuthType, ...restServerExtension} = serverExtension || {};

                        const requestParams = {
                            workspaceId: spaceId,
                            serverStatus: 'draft',
                            labels: spaceLabels?.filter(({id}) =>
                                labels?.includes(id)).map(({id, labelValue}) => ({id, labelValue})
                            ),
                            ...data,
                            serverAuthType,
                            serverConf: {
                                serverSourceType,
                                overview,
                                apiInfos,
                                serverConfig: serverConfigParse(serverConfig, values.serverProtocolType),
                                serverExtension: restServerExtension,
                            },
                        };
                        if (mode === 'external') {
                            // 标准MCP的全局变量需在前端静默初始化。不然用户在编辑时，该字段无值。
                            requestParams.serverParams = extractServerParams(
                                serverConfig,
                                values.serverProtocolType
                            );
                        }
                        apiPostMCPServer(requestParams).then(
                            mcp => {
                                callback?.(mcp);
                            }
                        ).catch(
                            e => {
                                callback?.(e);
                            }
                        );
                    } catch (e) {
                        console.error(e);
                        return new Error(e);
                    }
                }
            ).catch(
                e => {
                    console.error(e);
                    message.error('请检查表单');
                    onError?.();
                }
            );
        },
        [form, workspaceId]
    );
    if (!form) {
        return;
    }
    return handleCreate;

};
