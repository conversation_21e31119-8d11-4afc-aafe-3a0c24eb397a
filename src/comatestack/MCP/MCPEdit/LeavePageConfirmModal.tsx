/* eslint-disable max-lines */
import {But<PERSON>, Modal} from '@panda-design/components';
import {Checkbox, Flex, Form, Space} from 'antd';
import styled from '@emotion/styled';
import {useCallback, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {useSearchParams} from '@panda-design/router';
import {useBoolean} from 'huse';
import {IconAlert} from '@/icons/mcp';
import {apiPutMCPServer} from '@/api/mcp';
import {useMCPServerId} from '@/components/MCP/hooks';
import {useMCPServer, loadMCPServer} from '@/regions/mcp/mcpServer';
import {useSpaceLabels} from '@/regions/mcp/mcpSpace';
import {MCPEditTab} from '@/types/mcp/mcp';
import {serverConfigParse} from '../MCPCreate/hooks';
import {resetTouchedBasePath} from './regions';
import {useHandleSaveTool} from './ToolsContent/hooks';

const Content = styled(Flex)`
    svg{
        width: 22px;
        height: 22px;
        color: #F58300;
    }
`;

interface Props {
    nextLocation: string;
    onCancel: () => void;
    type: 'notSaved' | 'invalidForm';
    resetForm: () => void;
}

const invalidFormInfo = '当前页面有未通过校验的信息，无法保存，请确认是否继续离开？';
const notSavedFormInfo = 'MCP Server发生修改尚未保存，继续离开修改信息不会被保存。确定继续离开吗？';

const Footer = ({onCancel, nextLocation, type, resetForm}: Props) => {
    const [loading, {on, off}] = useBoolean();
    const [checked, setChecked] = useState(false);
    const {validateFields} = Form.useFormInstance();
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const spaceLabels = useSpaceLabels(mcpServer?.workspaceId);
    const {activeTab} = useSearchParams();
    const navigate = useNavigate();
    const saveTool = useHandleSaveTool({on, off});
    const handleFnWidthNotConfirm = (fn: () => void) => {
        return () => {
            if (checked) {
                localStorage.setItem('leavePageNotConfirm', 'true');
            }
            fn();
        };
    };
    const handleLeave = handleFnWidthNotConfirm(() => {
        resetTouchedBasePath();
        resetForm();
        setTimeout(
            () => {
                navigate(nextLocation);
            },
            10
        );
    });
    const handleCancel = handleFnWidthNotConfirm(onCancel);
    const save = useCallback(
        async () => {
            if (activeTab === MCPEditTab.ServerInfo) {
                on();
                const values = await validateFields();
                const {serverInfo} = values;
                const {serverConf, labels} = serverInfo;
                const {serverConfig, serverExtension} = serverConf ?? {};

                const {serverAuthType, ...restServerExtension} = serverExtension || {};

                await apiPutMCPServer({
                    mcpServerId,
                    ...mcpServer,
                    ...serverInfo,
                    serverAuthType,
                    labels: spaceLabels?.filter(({id}) =>
                        labels.includes(id)).map(({id, labelValue}) => ({id, labelValue})
                    ),
                    serverConf: {
                        ...mcpServer.serverConf,
                        ...serverConf,
                        serverConfig: serverConfigParse(serverConfig, serverInfo.serverProtocolType),
                        serverExtension: restServerExtension,
                    },
                });
                loadMCPServer({mcpServerId});
                resetTouchedBasePath();
                setTimeout(
                    () => {
                        navigate(nextLocation);
                    },
                    10
                );
                off();
            } else if (activeTab === 'tools') {
                saveTool().then(
                    () => {
                        resetTouchedBasePath();
                        setTimeout(
                            () => {
                                navigate(nextLocation);
                            },
                            10
                        );
                    }
                );
            }
        },
        [activeTab, mcpServer, mcpServerId, navigate, nextLocation, off, on, saveTool, spaceLabels, validateFields]
    );
    const handelSave = handleFnWidthNotConfirm(save);
    return (
        <Space>
            {
                type === 'notSaved' && (
                    <Checkbox checked={checked} onChange={e => setChecked(e.target.checked)}>不再提示</Checkbox>
                )
            }
            <Button onClick={handleCancel} loading={loading}>取消</Button>
            {
                type === 'notSaved' && (
                    <Button type="primary" onClick={handelSave} loading={loading}>保存并离开</Button>
                )
            }
            <Button type="primary" onClick={handleLeave} loading={loading}>继续离开</Button>
        </Space>
    );
};

const LeavePageConfirmModal = ({onCancel, nextLocation, type, resetForm}: Props) => {
    return (
        <Modal
            open
            onCancel={onCancel}
            width={450}
            closable={false}
            footer={<Footer nextLocation={nextLocation} onCancel={onCancel} type={type} resetForm={resetForm} />}
        >
            <Content gap={8}>
                <IconAlert />
                <span>
                    {type === 'notSaved' ? notSavedFormInfo : invalidFormInfo}
                </span>
            </Content>
        </Modal>
    );
};

export default LeavePageConfirmModal;
