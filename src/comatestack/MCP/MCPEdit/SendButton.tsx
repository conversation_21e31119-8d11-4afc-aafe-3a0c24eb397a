/* eslint-disable max-lines */
import {Button, message, Modal} from '@panda-design/components';
import {Alert, Checkbox, Flex, Form, Radio, Select} from 'antd';
import {useBoolean, useRequest} from 'huse';
import {useCallback, useEffect} from 'react';
import {IconSend} from '@/icons/comatestack';
import {Gap} from '@/design/iplayground/Gap';
import {useMCPServerId} from '@/components/MCP/hooks';
import {apiGetAllZones, apiGetPublishInfo, apiPutServerPublish, PublishInfo} from '@/api/mcp';
import {loadMCPServer} from '@/regions/mcp/mcpServer';
import {UserMultipleSelect} from '@/components/ievalue/UserMultipleSelect';
import {EmailGroupSelect} from '@/components/MCP/EmailGroupSelect';

enum PublishType {
  HUB = 'hub',
  WORKSPACE = 'workspace'
}

enum VisibilityType {
  PUBLIC = 'PUBLIC',
  RANGE = 'RANGE',
}

enum ChannelType {
    SPACE = 'space',
    HUB = 'hub',
    ZONE = 'zone'
}

const CHANNEL_OPTIONS = [
    // {label: '本空间', value: ChannelType.SPACE, disabled: true},
    {label: '发布到广场', value: ChannelType.HUB},
    // {label: '发布到专区', value: ChannelType.ZONE},
];

interface Props {
    onCancel: () => void;
}

interface FormValues extends Omit<PublishInfo, 'rangeContent'|'publishType'> {
    rangeContent?: string[];
    channel: string[];
}

const getInitialValues = (values?: PublishInfo): FormValues => {
    if (values) {
        return {
            channel: values.publishType === PublishType.WORKSPACE
                ? [] : [ChannelType.HUB],
            visibilityType: values.visibilityType,
            contacts: values.contacts,
            rangeContent: values.visibilityType === VisibilityType.RANGE ? values.rangeContent.content : undefined,
            zoneId: values.zoneId,
        };
    }
    return {
        channel: [ChannelType.HUB],
        visibilityType: VisibilityType.PUBLIC,
        contacts: [],
    };
};

const transformPublishParams = (values: FormValues, mcpServerId: number) => {
    return {
        mcpServerId,
        visibilityType: values.visibilityType === 'ZONE' ? VisibilityType.PUBLIC : values.visibilityType,
        publishType: values.channel?.includes(ChannelType.HUB) ? PublishType.HUB : PublishType.WORKSPACE,
        contacts: values.contacts,
        zoneId: values.zoneId,
        ...values.visibilityType === VisibilityType.RANGE ? {
            rangeContent: {
                rangeType: 'EMAIL_GROUP',
                content: values.rangeContent,
            },
        } : {},
    };
};

const getAllZones = () => apiGetAllZones().then(res => {
    const flatten: Array<{label: string, value: string}> = [];
    // 只有两层，无需递归
    res.forEach(zone => {
        flatten.push({label: zone.name, value: zone.id});
        zone.childZones.forEach(child => {
            flatten.push({label: child.name, value: child.id});
        });
    });
    return flatten;
});

const Range = () => {
    const {data: zones} = useRequest(getAllZones, {});
    return (
        <>
            <Form.Item
                labelCol={{span: 4}}
                label="专区"
                name="zoneId"
            >
                <Select options={zones} allowClear placeholder="请选择专区" />
            </Form.Item>
            <Form.Item label="发布范围" labelCol={{span: 4}} required>
                <Flex vertical gap={8}>
                    <Form.Item name="visibilityType" style={{marginBottom: 0}}>
                        <Radio.Group>
                            <Radio value={VisibilityType.PUBLIC}>公开</Radio>
                            <Radio value={VisibilityType.RANGE}>
                                指定范围
                            </Radio>
                        </Radio.Group>
                    </Form.Item>
                    {/* 使用dependencies有时会漏掉一些更新，故使用shouldUpdate */}
                    <Form.Item noStyle shouldUpdate={(pre, cur) => pre.visibilityType !== cur.visibilityType}>
                        {({getFieldValue}) => {
                            const visibilityTypeValue = getFieldValue('visibilityType');
                            return visibilityTypeValue === VisibilityType.RANGE ? (
                                <Form.Item
                                    name="rangeContent"
                                    rules={[{
                                        required: true,
                                        message: '请选择邮箱或邮件组',
                                    }]}
                                >
                                    <EmailGroupSelect />
                                </Form.Item>
                            ) : null;
                        }}
                    </Form.Item>
                </Flex>
            </Form.Item>
        </>
    );
};

const ChannelGroup = ({value, onChange}: any) => {
    return (
        <>
            <Checkbox checked disabled>本空间</Checkbox>
            <Checkbox.Group options={CHANNEL_OPTIONS} value={value} onChange={onChange} />
        </>
    );
};

const ConfirmModal = ({onCancel}: Props) => {
    const [form] = Form.useForm<FormValues>();
    const mcpServerId = useMCPServerId();
    const {data: publishInfo} = useRequest(apiGetPublishInfo, {mcpServerId});
    useEffect(
        () => {
            form.setFieldsValue(getInitialValues(publishInfo));
        },
        [form, publishInfo]
    );
    const handleOk = useCallback(
        async () => {
            try {
                const values = await form.validateFields();
                await apiPutServerPublish(transformPublishParams(values, mcpServerId));
                loadMCPServer({mcpServerId});
                message.success('发布成功');
                onCancel();
            } catch (e) {
                if (e.errorFields) {
                    // 说明是表单验证失败
                } else {
                    message.error('发布失败:', e.response?.data?.msg ?? '');
                }
            }
        },
        [mcpServerId, onCancel, form]
    );

    return (
        <Modal
            title="发布"
            open
            onCancel={onCancel}
            onOk={handleOk}
        >
            <Alert message="MCP Server需发布后才可以调用，发布到广场可以共享给其他开发者使用。" />
            <Gap />
            {/* 这里必须强制设置disabled为true，
                因为该组件嵌套在MCPEdit组件下,
                而MCPEdit在中有一个form实例,
                在某些情况下MCPEdit中的实例会被设为disabled,
                从而影响此处的form实例。
            */}
            <Form form={form} name="release" disabled={false}>
                <Form.Item label="发布渠道" name="channel" labelCol={{span: 4}} required>
                    <ChannelGroup />
                </Form.Item>
                <Form.Item noStyle dependencies={['channel']}>
                    {({getFieldValue}) => {
                        const channelValue = getFieldValue('channel');
                        const publishToHub = channelValue?.[0] === ChannelType.HUB;
                        return publishToHub ? <Range /> : null;
                    }}
                </Form.Item>
                <Form.Item
                    labelCol={{span: 4}}
                    name="contacts"
                    label="联系人"
                    rules={[{required: true, message: '请选择联系人'}]}
                >
                    <UserMultipleSelect mode="multiple" placeholder="请输入该MCP Server的联系人，方便使用者咨询" labelInValue={false} />
                </Form.Item>
            </Form>
        </Modal>
    );
};

const SendButton = () => {
    const [open, {on, off}] = useBoolean();
    return (
        <>
            <Button
                disabled={false}
                type="primary"
                onClick={on}
                icon={<IconSend />}
            >
                发布
            </Button>
            {/* 用实例的创建与销毁来控制其与生命周期相关的代码逻辑，而非使用open类的入参控制 */}
            {open && <ConfirmModal onCancel={off} />}
        </>
    );
};

export default SendButton;
