/* eslint-disable complexity */
import {css} from '@emotion/css';
import {FormInstance} from 'antd';
import {message} from '@panda-design/components';
import {keys} from 'lodash';
import {BaseParam, MCPEditTab} from '@/types/mcp/mcp';
import {setTouchedBasePath} from './regions';

export const errorRowCss = css`
    td {
        &: first-child {
            border-left: 2px solid #e62c4b !important;
        }
    }
`;

const serverInfoHandler = (field: any) => {
    const name = field[0]?.name;
    const stringName = name?.join('.');
    if (stringName?.includes('serverInfo')) {
        setTouchedBasePath(MCPEditTab.ServerInfo);
    }
};

const toolsHandler = (field: any) => {
    const name = field[0]?.name;
    const stringName = name?.join('.');
    if (stringName?.includes('tools')) {
        setTouchedBasePath(MCPEditTab.Tools);
    }
};

const toolParamsHandler = (field: any) => {
    const name = field[0]?.name;
    const stringName = name?.join('.');
    if (stringName?.includes('toolParams.toolParams')) {
        const rows = document.querySelectorAll('.toolParamsTableRow');
        rows?.[name?.[name?.length - 2]]?.classList.remove(errorRowCss);
    }
};

const createConfigParams = (serverParams: BaseParam[]) => {
    const merged = serverParams.reduce(
        (acc, cur) => {
            return {
                ...acc,
                [cur.name]: cur.description ?? '',
            };
        },
        {}
    );
    return merged;
};

const getNewConfigParams = (serverParams: BaseParam[], serverProtocolType: string) => {
    const newConfigParams: {
        headers?: any;
        env?: any;
    } = {};
    if (serverProtocolType === 'SSE' || serverProtocolType === 'Streamable_HTTP') {
        const headers = createConfigParams(serverParams);
        newConfigParams.headers = headers;
    } else if (serverProtocolType === 'STDIO') {
        const env = createConfigParams(serverParams);
        newConfigParams.env = env;
    }
    return newConfigParams;
};

const setServerConfigValue = (
    form: FormInstance,
    options: {headers?: any, env?: any, config: any, serverKey: string}
) => {
    form.setFieldValue(
        ['serverInfo', 'serverConf', 'serverConfig'],
        JSON.stringify(
            {
                mcpServers: {
                    [options.serverKey]: {
                        ...options.config,
                        headers: options.headers,
                        env: options.env,
                    },
                },
            },
            null,
            4
        )
    );
};

const serverParamsHandler = (field: any, form: FormInstance) => {
    const name = field[0]?.name;
    const stringName = name?.join('.');
    const serverSourceType = form.getFieldValue('serverSourceType');
    const serverKey = form.getFieldValue(['serverInfo', 'serverKey']);
    if (stringName?.includes('serverInfo.serverParams') && serverKey && serverSourceType === 'external') {
        const serverProtocolType = form.getFieldValue(['serverInfo', 'serverProtocolType']);
        const serverParams: BaseParam[] = form.getFieldValue(['serverInfo', 'serverParams']);
        const serverConfig = form.getFieldValue(['serverInfo', 'serverConf', 'serverConfig']);
        const filteredParams = serverParams?.filter(item => item.name) ?? [];
        try {
            const parserServerConfig = JSON.parse(serverConfig);
            const serverKeys = keys(parserServerConfig.mcpServers);
            const serverKey = serverKeys[0];
            const config = parserServerConfig.mcpServers[serverKey];
            const newConfigParams = getNewConfigParams(filteredParams, serverProtocolType);
            setServerConfigValue(form, {
                config,
                headers: newConfigParams.headers,
                env: newConfigParams.env,
                serverKey,
            });
        } catch (e) {
            if (e instanceof SyntaxError && e.message.includes('in JSON at')) {
                message.error('服务器配置JSON格式错误，无法自动同步数据，请手动修改');
            } else {
                message.error('服务器配置内容不完成，无法自动同步数据，请手动修改');
            }
            console.error(e);
        }
    }
};

export const onValueChange = (field: any, form: FormInstance) => {
    {
        if (field.length === 1) {
            serverInfoHandler(field);
            toolsHandler(field);
            toolParamsHandler(field);
            serverParamsHandler(field, form);
        }
    }
};
