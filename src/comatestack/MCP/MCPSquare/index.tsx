import styled from '@emotion/styled';
import {Flex} from 'antd';
import {useEffect} from 'react';
import {css} from '@emotion/css';
import {FetchMCPServersProvider} from '@/components/MCP/MCPServerCard/FetchMCPServersProvider';
import MCPServerList from '@/components/MCP/MCPServerCard/MCPServerList';
import MCPServerFilter, {FilterValues} from '@/components/MCP/MCPServerFilter';
import {apiGetAllZones, apiGetDefaultLabels} from '@/api/mcp';
import {ServerType, ServerProtocol, Order} from '@/components/MCP/MCPServerFilter/constant';
import {ALL_LABELS, OTHER_LABELS} from '@/components/MCP/MCPServerFilter/LabelsFilterContent';
import {SpaceLabel} from '@/types/mcp/mcp';
import RegionNavigation from './RegionNavigation';

const Container = styled(Flex)`
    width: 100%;
    height: calc(100vh - 48px);
    padding: 0 32px 16px;
    overflow: auto;
`;

const initialTabFormValue = {tab: 'all'};
const initialFilterFormValue: FilterValues = {
    serverSourceType: ServerType.ALL,
    serverProtocolType: ServerProtocol.ALL,
    labels: [-2],
    order: Order.DEFAULT,
};
const initialSearchParams = {
    ...initialTabFormValue,
    ...initialFilterFormValue,
};

const fetchSquareScenes = () => {
    return apiGetDefaultLabels().then(res => {
        return [
            {labelValue: '全部', id: ALL_LABELS, labelType: 'GLOABL'},
            ...res,
            {labelValue: '其他', id: OTHER_LABELS, labelType: 'GLOABL'},
        ];
    }).catch(error => {
        window.console.error(error);
        return [
            {labelValue: '全部', id: ALL_LABELS, labelType: 'GLOABL'},
            {labelValue: '其他', id: OTHER_LABELS, labelType: 'GLOABL'},
        ];
    }) as Promise<SpaceLabel[]>;
};

const serverFilterCss = css`
    margin-top: 16px;
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: #fff;
    padding-bottom: 16px;
`;

const MCPSquare = () => {
    useEffect(
        () => {
            apiGetAllZones();
        },
        []
    );
    return (
        <FetchMCPServersProvider initParams={initialSearchParams} platformType="hub" fetchScenes={fetchSquareScenes}>
            <Container vertical id="scrollableDiv">
                <RegionNavigation />
                <MCPServerFilter
                    className={serverFilterCss}
                    initialFilterFormValue={initialFilterFormValue}
                    initialTabFormValue={initialTabFormValue}
                />
                <MCPServerList scrollableTarget="scrollableDiv" />
            </Container>
        </FetchMCPServersProvider>

    );
};

export default MCPSquare;
