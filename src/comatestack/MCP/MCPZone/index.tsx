import {useCallback, useEffect, useMemo} from 'react';
import {useNavigate} from 'react-router-dom';
import {useRequestCallback} from 'huse';
import styled from '@emotion/styled';
import {css, cx} from '@emotion/css';
import {useMCPZoneId} from '@/components/MCP/hooks';
import {MCPZoneLink} from '@/links/mcp';
import {
    FetchMCPServersProvider,
} from '@/components/MCP/MCPServerCard/FetchMCPServersProvider';
import MCPServerFilter, {FilterValues} from '@/components/MCP/MCPServerFilter';
import MCPServerList from '@/components/MCP/MCPServerCard/MCPServerList';
import {apiGetMCPZoneDetail, apiGetZoneScenes} from '@/api/mcp';
import {Order, ServerProtocol, ServerType} from '@/components/MCP/MCPServerFilter/constant';
import {ALL_LABELS, OTHER_LABELS} from '@/components/MCP/MCPServerFilter/LabelsFilterContent';
import {SpaceLabel} from '@/types/mcp/mcp';
import {ZoneHeader} from './ZoneHeader';

const DEVELOP_SECTION_ID = '';

const initialTabFormValue = {tab: 'all'};
const initialFilterFormValue: FilterValues = {
    serverSourceType: ServerType.ALL,
    serverProtocolType: ServerProtocol.ALL,
    labels: [-2],
    order: Order.DEFAULT,
};
const initialSearchParams = {
    ...initialTabFormValue,
    ...initialFilterFormValue,
};

const Container = styled.div`
    width: 100%;
    height: calc(100vh - 48px);
    overflow: auto;
`;

const paddingCss = css`
    padding: 0 20px 16px;
`;

const serverFilterCss = css`
    margin-top: 16px;
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: #fff;
    padding-bottom: 16px;
`;

const MCPZone = () => {
    const mcpZoneId = useMCPZoneId();
    const navigate = useNavigate();
    const [fetchZone, {data: zone}] = useRequestCallback(apiGetMCPZoneDetail, mcpZoneId);
    const fetchZoneScenes = useCallback(
        () => {
            return apiGetZoneScenes({zoneId: mcpZoneId}).then(res => {
                return [
                    {labelValue: '全部', id: ALL_LABELS, labelType: 'GLOABL'},
                    ...res,
                    {labelValue: '其他', id: OTHER_LABELS, labelType: 'GLOABL'},
                ];
            }).catch(error => {
                window.console.error(error);
                return [
                    {labelValue: '全部', id: ALL_LABELS, labelType: 'GLOABL'},
                    {labelValue: '其他', id: OTHER_LABELS, labelType: 'GLOABL'},
                ];
            }) as Promise<SpaceLabel[]>;
        },
        [mcpZoneId]
    );
    useEffect(
        () => {
            if (!mcpZoneId) {
                navigate(MCPZoneLink.toUrl({zoneId: DEVELOP_SECTION_ID}));
            }
        },
        [mcpZoneId, navigate]
    );
    useEffect(
        () => {
            if (mcpZoneId) {
                fetchZone();
            }
        },
        [mcpZoneId, fetchZone]
    );
    const initParams = useMemo(
        () => {
            if (mcpZoneId) {
                return {
                    ...initialSearchParams,
                    zoneId: mcpZoneId,
                };
            }
            return initialSearchParams;
        },
        [mcpZoneId]
    );
    return (
        <FetchMCPServersProvider initParams={initParams} platformType="zone" fetchScenes={fetchZoneScenes}>
            <Container id="scrollableDiv">
                <ZoneHeader zone={zone} />
                <MCPServerFilter
                    className={cx(serverFilterCss, paddingCss)}
                    initialFilterFormValue={initialFilterFormValue}
                    initialTabFormValue={initialTabFormValue}
                />
                <div className={paddingCss}>
                    <MCPServerList scrollableTarget="scrollableDiv" />
                </div>
            </Container>
        </FetchMCPServersProvider>
    );
};

export default MCPZone;
