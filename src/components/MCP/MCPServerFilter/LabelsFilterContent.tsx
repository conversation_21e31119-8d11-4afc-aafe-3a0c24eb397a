import {useRequest} from 'huse';
import {CSSProperties, useCallback, useMemo} from 'react';
import {uniq} from 'lodash';
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {useFetchMCPServersContext} from '../MCPServerCard/FetchMCPServersProvider';


interface Props {
    value?: number[];
    onChange?: (value: number[]) => void;
    style?: CSSProperties;
    className?: string;
}

export const ALL_LABELS = -2;
export const OTHER_LABELS = -1;

const LabelTag = styled.div<{selected: boolean}>`
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    color: ${props => (props.selected ? '#0080FF' : '#000000')};
`;

const LabelContainer = styled(Flex)`
    ${LabelTag} {
        margin-right: 32px;
    }
`;

interface LabelProps {label: string, value: number, selected: boolean, onSelect: (value: number) => void}

const Label = ({label, value, selected, onSelect}: LabelProps) => {
    const handleSelect = useCallback(
        () => {
            window.console.log(1);
            onSelect(value);
        },
        [value, onSelect]
    );
    return (
        <LabelTag selected={selected} onClick={handleSelect}>
            {label}
        </LabelTag>
    );
};

export const LabelsFilterContent = ({value = [], onChange, style, className}: Props) => {
    const {fetchScenes} = useFetchMCPServersContext();
    const {data = []} = useRequest(fetchScenes, null);
    const options = useMemo(
        () => {
            return data.map(({labelValue, id}) => {
                return {
                    label: labelValue,
                    value: id,
                };
            });
        },
        [data]
    );
    const handleChange = useCallback(
        (labelValue: number) => {
            if (labelValue === ALL_LABELS || labelValue === OTHER_LABELS) {
                return onChange?.([labelValue]);
            } else if (value?.includes(labelValue)) {
                const result = value?.filter(i => i !== labelValue);
                onChange?.(result?.length > 0 ? result : [ALL_LABELS]);
            } else {
                onChange?.(uniq([...value?.filter(i => i !== ALL_LABELS && i !== OTHER_LABELS), labelValue]));
            }
        },
        [onChange, value]
    );
    return (
        <LabelContainer style={style} className={className}>
            {
                options.map(
                    option => (
                        <Label
                            key={option.value}
                            label={option.label}
                            value={option.value}
                            selected={value?.includes(option.value)}
                            onSelect={handleChange}
                        />
                    )
                )
            }
        </LabelContainer>
    );
};
