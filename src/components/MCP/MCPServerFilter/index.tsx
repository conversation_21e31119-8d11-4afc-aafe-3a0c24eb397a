/* eslint-disable max-lines */
import {Flex, Form, Input, Select} from 'antd';
import {SearchOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import {CSSProperties} from 'react';
import {css, cx} from '@emotion/css';
import TitleTabs from '@/components/MCP/TitleTabs';
import {MCPPlaygoundButton} from '@/components/MCP/MCPPlaygoundButton';
import {CreateMCPButton} from '@/components/MCP/CreateMCPButton';
import {useFetchMCPServersContext} from '../MCPServerCard/FetchMCPServersProvider';
import {LabelsFilterContent} from './LabelsFilterContent';
import {
    ServerType,
    ServerProtocol,
    Order,
    serverTypeFilterOptions,
    serverProtocolFilterOptions,
    compositeFilterOptions,
    serverTabItems,
} from './constant';

export interface FilterValues {
    serverSourceType: ServerType;
    serverProtocolType: ServerProtocol;
    labels: number[];
    order: Order;
}

export interface TabValues {
    tab: string;
    keywords?: string;
    favorite?: boolean;
    isMine?: boolean;
}

const StyledForm = styled(Form)`
    .ant-5-form-item {
        margin-bottom: 0;
    }
    margin-top: 16px;
`;

const StyledSelect = styled(Select)`
    width: 200px!important;
`;

const formLabelSplitCss = css`
    .ant-5-form-item-no-colon:after{
        content: '';
        line-height: 20px;
        border-left: 1px solid #D9D9D9;
        left: 16px;
    }
`;

const formlabelCss = css`
    .ant-5-form-item-no-colon{
        margin-right: 6px;
    }
    .ant-5-form-item-no-colon:before{
        width: 0px !important;
        margin-right: 0px !important;
    }
`;

const formItemCss = css`
    margin-left: 32px !important;
`;

interface Props {
    initialTabFormValue: TabValues;
    initialFilterFormValue: FilterValues;
    style?: CSSProperties;
    className?: string;
}
const MCPServerFilter = ({initialTabFormValue, initialFilterFormValue, style, className}: Props) => {
    const [tabForm] = Form.useForm();
    const [filterForm] = Form.useForm();
    const {changeSearchParams} = useFetchMCPServersContext();
    const onFormChange = (name: string, info: any) => {
        const changed = {
            [info.changedFields[0].name[0]]: info.changedFields[0].value,
        };
        changeSearchParams(changed);
    };
    return (
        <Form.Provider onFormChange={onFormChange}>
            <div style={style} className={className}>
                <Form
                    name="tab"
                    form={tabForm}
                    initialValues={initialTabFormValue}
                >
                    <Form.Item name="tab" noStyle>
                        <TitleTabs
                            tabBarExtraContent={(
                                <Flex align="center" gap={8} style={{marginTop: 8}}>
                                    <Form.Item name="keywords" noStyle>
                                        <Input
                                            autoFocus
                                            style={{width: 250}}
                                            placeholder="请输入MCP名称/描述搜索"
                                            suffix={<SearchOutlined />}
                                            allowClear
                                        />
                                    </Form.Item>
                                    <CreateMCPButton />
                                    <MCPPlaygoundButton />
                                </Flex>
                            )}
                            items={serverTabItems}
                        />
                    </Form.Item>
                </Form>
                <StyledForm
                    form={filterForm}
                    initialValues={initialFilterFormValue}
                    name="filter"
                >
                    <Form.Item className={cx(formlabelCss, formLabelSplitCss)} name="labels" label="场景" colon={false}>
                        <LabelsFilterContent style={{marginLeft: '16px'}} />
                    </Form.Item>
                    <Flex justify="space-between" style={{marginTop: '16px'}}>
                        <Flex>
                            <Form.Item
                                className={formlabelCss}
                                name="serverSourceType"
                                label="类型"
                                colon={false}
                            >
                                <StyledSelect options={serverTypeFilterOptions} />
                            </Form.Item>
                            <Form.Item
                                className={cx(formlabelCss, formItemCss)}
                                name="serverProtocolType"
                                label="协议"
                                colon={false}
                            >
                                <StyledSelect options={serverProtocolFilterOptions} />
                            </Form.Item>
                        </Flex>
                        <Form.Item
                            className={formlabelCss}
                            name="order"
                            label="排序"
                            colon={false}
                        >
                            <Select style={{width: '150px'}} options={compositeFilterOptions} />
                        </Form.Item>
                        {/* <Flex>
                            <CompositeFilterFormItem />
                        </Flex> */}
                    </Flex>
                </StyledForm>
            </div>
        </Form.Provider>
    );
};

export default MCPServerFilter;
