import styled from '@emotion/styled';
import {Tabs, TabsProps} from 'antd';

const StyledTabs = styled(Tabs)`
    position: sticky;
    top: 16;
    height: 54px;
    .ant-5-tabs-tab-btn {
        font-size: 16ps;
        color: #5C5C5C;
        line-height: 36px;
    }
    .ant-5-tabs-tab-active {
        font-size: 24px !important;
        font-weight: 600 !important;
    }
    .ant-5-tabs-ink-bar{
        height: 8px !important;
        border-radius: 6px;
        background: linear-gradient(251.92deg, #00DAE5 -9.28%, #00B3FF 38.44%, #0080FF 91.87%);
    }
`;


const TitleTabs = (props: TabsProps) => {
    return (
        <StyledTabs {...props} />
    );
};

export default TitleTabs;

