import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {Flex, Typography} from 'antd';

const StyledFlex = styled(Flex)`
    background-color: #FAFAFA;
    border-radius: 4px;
    height: 200px;
`;

interface Props {
    onAdd?: () => void;
}
export default function MCPServerListEmpty({onAdd}: Props) {
    return (
        <StyledFlex vertical align="center" justify="center" gap={6} style={{padding: '16px 0'}}>
            <Typography.Paragraph type="secondary">
                点击
                <Button type="link" variant="link" onClick={onAdd}>
                    一键添加官方示例
                </Button>
                ，跳过鉴权快速开始体验
            </Typography.Paragraph>
        </StyledFlex>
    );
}
