import {useEffect} from 'react';
import {MCPServerBase} from '@/types/mcp/mcp';
import {MCPServerListType} from './ServerListTabs';
import {setMCPServerAtom} from './region';

interface UseInitServerListParams {
    open: boolean;
    request: () => void;
    requestExamples: () => void;
    initialValue: number[];
    type?: MCPServerListType;
    servers?: MCPServerBase[];
    exampleServers: MCPServerBase[];
    setSelectedRowKeys: (value: number[]) => void;
    batchAddExampleServers: boolean;
}
/* 加载组件时初始化表格数据和选中值 */
export const useInitServerList = ({
    open,
    request,
    servers,
    requestExamples,
    initialValue,
    setSelectedRowKeys,
    exampleServers,
    batchAddExampleServers,
}: UseInitServerListParams) => {
    useEffect(
        () => {
            if (open) {
                request();
                requestExamples();
            }
        },
        [open, request, requestExamples]
    );
    useEffect(
        () => {
            if (open) {
                setSelectedRowKeys(initialValue);
            }
        },
        [open, initialValue, setSelectedRowKeys]
    );
    useEffect(
        () => {
            if (batchAddExampleServers && open) {
                setSelectedRowKeys(exampleServers?.map((item: MCPServerBase) => item.id));
            }
        },
        [open, exampleServers, setSelectedRowKeys, batchAddExampleServers]
    );

    useEffect(
        () => {
            if (servers) {
                servers?.forEach(
                    server => setMCPServerAtom(server.id, server)
                );
            }
        },
        [servers]
    );

    useEffect(
        () => {
            if (exampleServers) {
                exampleServers?.forEach(
                    server => setMCPServerAtom(server.id, server)
                );
            }
        },
        [exampleServers]
    );
};
